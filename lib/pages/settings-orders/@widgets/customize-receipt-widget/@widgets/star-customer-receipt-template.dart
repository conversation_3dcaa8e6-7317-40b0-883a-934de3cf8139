// ignore_for_file: inference_failure_on_instance_creation

import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:diacritic/diacritic.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:image/image.dart' as img;
import 'package:starxpand/models/starxpand_printer.dart';
import 'package:stickyqrbusiness/@common/_local-storage.dart';
import 'package:stickyqrbusiness/@core/models/order.model.dart';
import 'package:stickyqrbusiness/@core/models/ordering-modifier.model.dart';
import 'package:stickyqrbusiness/@core/models/receipt-template.model.dart';
import 'package:stickyqrbusiness/@core/store/auth/auth_bloc.dart';
import 'package:stickyqrbusiness/@utils/currency-input-formatter/extensions/string-extensions.dart';
import 'package:stickyqrbusiness/@utils/currency.dart';
import 'package:stickyqrbusiness/@utils/datetime.dart';
import 'package:stickyqrbusiness/@utils/logger.dart';
import 'package:stickyqrbusiness/@utils/validations.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';

enum ImageAlignment {
  left,
  center,
  right,
}

// Enum để định nghĩa các kích thước font
enum StarFontSize {
  normal,     // 1x1
  wide,       // 2x1 (rộng gấp đôi)
  tall,       // 1x2 (cao gấp đôi)
  large,      // 2x2 (lớn gấp đôi cả chiều)
  extraWide,  // 3x1
  extraTall,  // 1x3
  extraLarge, // 3x3
}

// Enum để định nghĩa các style text
enum TextPrintStyle {
  normal,
  bold,
  underline,
  boldUnderline,
  inverse,
}

// Enum cho alignment
enum TextAlignment {
  left,
  center,
  right,
}

// Enum cho các mode in hình ảnh
enum ImagePrintMode {
  bitImage,  // ESC * bit image mode
  graphics,  // GS v raster mode  
  ascii,     // ASCII art fallback
}

// Class để quản lý font settings
class FontSettings {
  final StarFontSize size;
  final TextPrintStyle style;
  final bool emphasized; // In đậm hơn (emphasized)
  final bool doubleStrike; // Gạch đôi
  
  const FontSettings({
    this.size = StarFontSize.normal,
    this.style = TextPrintStyle.normal,
    this.emphasized = false,
    this.doubleStrike = false,
  });
  
  // Factory constructors cho các style thường dùng
  factory FontSettings.title() => const FontSettings(
    size: StarFontSize.large,
    style: TextPrintStyle.bold,
    emphasized: true,
  );
  
  factory FontSettings.subtitle() => const FontSettings(
    size: StarFontSize.wide,
    style: TextPrintStyle.bold,
  );
  
  factory FontSettings.total() => const FontSettings(
    size: StarFontSize.wide,
    style: TextPrintStyle.bold,
    emphasized: true,
  );
  
  factory FontSettings.description() => const FontSettings(
    size: StarFontSize.normal,
    style: TextPrintStyle.underline,
  );
}

// Main StarPrinterReceipt class
class StarPrinterReceipt {
  static const int _defaultPaperWidth = 44;
  static const int _defaultPort = 9100;
  
  final String? ipAddress;
  final int port;
  final int paperWidth;
  
  Socket? _socket;
  
  StarPrinterReceipt({
    this.ipAddress,
    this.port = _defaultPort,
    this.paperWidth = _defaultPaperWidth,
  });

  final Order orderTest = Order(
    orderNumber: '123',
    subtotal: 125.99,
    taxTotal: 10.99,
    total: 136.98,
    customerName: 'Test Printer',
    items: [
      ItemOrder(
        title:
            'Charbroiled Pork, Beef or Chicken & Charbroiled Shrimp over Vermicelli',
        subtotal: 71,
        quantity: 1,
        total: 71,
        unitPrice: 71,
        modifiers: [
          ModifierOrder(
            price: 10,
            quantity: 1,
            taxRate: 0,
            taxAmount: 0,
            taxInclusive: false,
            modifier: ModifierItem(
                name: 'Options', maxFree: 1, options: [], products: []),
            option: ModifierOption(
              name: 'Chow Mein',
              price: 10,
              currencyCode: 'USD',
            ),
          ),
        ],
      ),
      ItemOrder(
        title: 'Salad Mix with Grilled Pork/Chicken',
        subtotal: 25,
        quantity: 1,
        total: 25,
        unitPrice: 25,
        modifiers: [
          ModifierOrder(
            price: 10,
            quantity: 1,
            taxRate: 0,
            taxAmount: 0,
            taxInclusive: false,
            modifier: ModifierItem(name: 'Options', maxFree: 1, options: [], products: []),
            option: ModifierOption(
              name: 'Chow Mein',
              price: 10,
              currencyCode: 'USD',
            ),
          ),
        ],
      ),
    ],
  );
  
  // ================== CONNECTION MANAGEMENT ==================
  
  /// Kết nối đến máy in
  Future<bool> connect({
    String? ip,
    int? portNumber
  }) async {
    try {
      _socket = await Socket.connect(ip ?? ipAddress, portNumber ?? port);
      return true;
    } catch (e) {
      print('Lỗi kết nối máy in: $e');
      return false;
    }
  }
  
  /// Ngắt kết nối
  Future<void> disconnect() async {
    try {
      await _socket?.flush();
      await _socket?.close();
      _socket = null;
    } catch (e) {
      print('Lỗi ngắt kết nối: $e');
    }
  }
  
  /// Kiểm tra kết nối
  bool get isConnected => _socket != null;
  
  // ================== FONT CONTROL COMMANDS ==================
  
  /// Lấy command cho font size
  List<int> _getStarFontSizeCommand(StarFontSize size) {
    switch (size) {
      case StarFontSize.normal:
        return [0x1B, 0x69, 0x00, 0x00]; // ESC i 0 0
      case StarFontSize.wide:
        return [0x1B, 0x69, 0x01, 0x00]; // ESC i 1 0
      case StarFontSize.tall:
        return [0x1B, 0x69, 0x00, 0x01]; // ESC i 0 1
      case StarFontSize.large:
        return [0x1B, 0x69, 0x01, 0x01]; // ESC i 1 1
      case StarFontSize.extraWide:
        return [0x1B, 0x69, 0x02, 0x00]; // ESC i 2 0
      case StarFontSize.extraTall:
        return [0x1B, 0x69, 0x00, 0x02]; // ESC i 0 2
      case StarFontSize.extraLarge:
        return [0x1B, 0x69, 0x02, 0x02]; // ESC i 2 2
    }
  }
  
  /// Lấy command cho text style
  List<int> _getStyleCommand(TextPrintStyle style, bool enable) {
    switch (style) {
      case TextPrintStyle.normal:
        return enable ? [] : [0x1B, 0x46, 0x1B, 0x2D, 0x00];
      case TextPrintStyle.bold:
        return enable ? [0x1B, 0x45] : [0x1B, 0x46];
      case TextPrintStyle.underline:
        return enable ? [0x1B, 0x2D, 0x01] : [0x1B, 0x2D, 0x00];
      case TextPrintStyle.boldUnderline:
        return enable ? [0x1B, 0x45, 0x1B, 0x2D, 0x01] : [0x1B, 0x46, 0x1B, 0x2D, 0x00];
      case TextPrintStyle.inverse:
        return enable ? [0x1B, 0x34] : [0x1B, 0x35];
    }
  }
  
  /// Lấy command cho emphasized
  List<int> _getEmphasizedCommand(bool enable) {
    return enable ? [0x1B, 0x45] : [0x1B, 0x46];
  }
  
  /// Lấy command cho double strike
  List<int> _getDoubleStrikeCommand(bool enable) {
    return enable ? [0x1B, 0x47] : [0x1B, 0x48];
  }
  
  /// Reset tất cả formatting
  List<int> _getResetCommand() {
    return [
      0x1B, 0x40, // ESC @ - Initialize
      0x1B, 0x69, 0x00, 0x00, // Normal font size
    ];
  }
  
  /// Áp dụng font settings
  List<int> _applyFontSettings(FontSettings settings) {
    final List<int> commands = [
      ..._getStarFontSizeCommand(settings.size),
      ..._getStyleCommand(settings.style, true),
    ];
    
    if (settings.emphasized) {
      commands.addAll(_getEmphasizedCommand(true));
    }
    
    if (settings.doubleStrike) {
      commands.addAll(_getDoubleStrikeCommand(true));
    }
    
    return commands;
  }
  
  /// Lấy alignment command
  List<int> _getAlignmentCommand(TextAlignment alignment) {
    switch (alignment) {
      case TextAlignment.left:
        return [0x1B, 0x1D, 0x61, 0x00];
      case TextAlignment.center:
        return [0x1B, 0x1D, 0x61, 0x01];
      case TextAlignment.right:
        return [0x1B, 0x1D, 0x61, 0x02];
    }
  }
  
  // ================== TEXT FORMATTING HELPERS ==================
  
  /// Tính toán width hiệu quả dựa trên font size
  int _getEffectiveWidth(StarFontSize fontSize) {
    switch (fontSize) {
      case StarFontSize.normal:
      case StarFontSize.tall:
        return paperWidth;
      case StarFontSize.wide:
      case StarFontSize.large:
        return paperWidth ~/ 2;
      case StarFontSize.extraWide:
      case StarFontSize.extraTall:
      case StarFontSize.extraLarge:
        return paperWidth ~/ 3;
    }
  }
  
  /// Wrap text theo width với smart wrapping
  List<String> _wrapText(String text, int maxWidth) {
    if (maxWidth <= 0) return [text];
    if (text.length <= maxWidth) {
      return [text];
    }
    
    final List<String> lines = [];
    final words = text.split(' ');
    String currentLine = '';
    
    for (final word in words) {
      if (word.length > maxWidth) {
        // Nếu từ dài hơn maxWidth, thêm dòng hiện tại (nếu có) và cắt từ
        if (currentLine.isNotEmpty) {
          lines.add(currentLine.trim());
          currentLine = '';
        }
        
        // Cắt từ dài thành nhiều dòng
        for (int i = 0; i < word.length; i += maxWidth) {
          final end = (i + maxWidth < word.length) ? i + maxWidth : word.length;
          lines.add(word.substring(i, end));
        }
      } else {
        // Kiểm tra xem có thể thêm từ vào dòng hiện tại không
        final testLine = currentLine.isEmpty ? word : '$currentLine $word';
        if (testLine.length <= maxWidth) {
          currentLine = testLine;
        } else {
          // Không thể thêm, lưu dòng hiện tại và bắt đầu dòng mới
          if (currentLine.isNotEmpty) {
            lines.add(currentLine.trim());
          }
          currentLine = word;
        }
      }
    }
    
    // Thêm dòng cuối cùng nếu có
    if (currentLine.isNotEmpty) {
      lines.add(currentLine.trim());
    }
    
    return lines.isEmpty ? [''] : lines;
  }
  
  /// Center text với width động dựa trên font size - FIX CHÍNH
  String _centerText(String text, StarFontSize fontSize) {
    final effectiveWidth = _getEffectiveWidth(fontSize);
    if (text.length >= effectiveWidth) return text;
    final padding = (effectiveWidth - text.length) ~/ 2;
    return ' ' * padding + text;
  }
  
  /// Right align text với width động dựa trên font size - FIX CHÍNH
  String _rightAlign(String text, StarFontSize fontSize) {
    final effectiveWidth = _getEffectiveWidth(fontSize);
    if (text.length >= effectiveWidth) return text;
    final spaces = effectiveWidth - text.length;
    return ' ' * spaces + text;
  }
  
  /// Format item line với price luôn cùng dòng đầu tiên
  List<String> _formatItemWithPrice(String itemText, String priceText) {
    final List<String> result = [];
    
    // Tính toán không gian có sẵn cho item text (trừ đi price và 1 space)
    final availableWidth = paperWidth - priceText.length - 1;
    
    if (availableWidth <= 0) {
      // Nếu không đủ chỗ, in riêng biệt
      result..addAll(_wrapText(itemText, paperWidth))
      ..add(_rightAlign(priceText, StarFontSize.normal)); // Use normal font for price alignment
      return result;
    }
    
    // Wrap item text theo available width
    final wrappedLines = _wrapText(itemText, availableWidth);
    
    if (wrappedLines.isEmpty) {
      result.add(_rightAlign(priceText, StarFontSize.normal));
      return result;
    }
    
    // Dòng đầu tiên: item text + spaces + price
    final firstLine = wrappedLines[0];
    final spacesNeeded = paperWidth - firstLine.length - priceText.length;
    final firstLineComplete = firstLine + (' ' * spacesNeeded) + priceText;
    result.add(firstLineComplete);
    
    // Các dòng còn lại chỉ có item text
    for (int i = 1; i < wrappedLines.length; i++) {
      result.add(wrappedLines[i]);
    }
    
    return result;
  }
  
  // ================== PUBLIC PRINTING METHODS ==================
  
  /// Initialize printer
  Future<void> initialize() async {
    if (!isConnected) return;
    
    final commands = <int>[]
    // ignore: prefer_spread_collections
    ..addAll(_getResetCommand())
    ..addAll([0x1B, 0x1D, 0x74, 0x00]) // UTF-8
    ..addAll([0x1B, 0x33, 0x18]); // Line spacing
    
    _socket!.add(commands);
  }
  
  /// In text với font settings - FIX CHÍNH: sử dụng effective width cho alignment
  Future<void> printText(
    String text, {
    FontSettings? fontSettings,
    TextAlignment alignment = TextAlignment.left,
    bool newLine = true,
    bool useEffectiveWidth = false,
  }) async {
    if (!isConnected) return;
    
    final settings = fontSettings ?? const FontSettings();
    final commands = <int>[]
    
    // Left align cho manual centering
    // ignore: prefer_inlined_adds
    ..addAll([0x1B, 0x1D, 0x61, 0x00]) // Force left align
    
    // Apply font settings
    ..addAll(_applyFontSettings(settings));
    
    // Tính width để wrap text
    final wrapWidth = useEffectiveWidth 
        ? _getEffectiveWidth(settings.size) 
        : paperWidth;
    
    // Wrap text
    final lines = _wrapText(removeDiacritics(text), wrapWidth);
    
    // Print từng dòng với manual alignment dựa trên font size
    for (final line in lines) {
      String finalLine = line;
      
      // Manual alignment với effective width dựa trên font size
      if (alignment == TextAlignment.center) {
        finalLine = _centerText(line, settings.size); // Sử dụng font size để tính width
      } else if (alignment == TextAlignment.right) {
        finalLine = _rightAlign(line, settings.size); // Sử dụng font size để tính width
      }
      
      commands.addAll(finalLine.codeUnits);
      if (newLine) commands.addAll('\n'.codeUnits);
    }
    
    _socket!.add(commands);
  }
  
  /// In dòng phân cách
  Future<void> printSeparator({String char = '-'}) async {
    if (!isConnected) return;
    
    await printText(char * paperWidth, fontSettings: const FontSettings());
  }
  
  /// In dòng trống
  // Future<void> printEmptyLine({int count = 1}) async {
  //   if (!isConnected) return;
    
  //   final commands = <int>[];
  //   for (int i = 0; i < count; i++) {
  //     commands.addAll('\n'.codeUnits);
  //   }
  //   _socket!.add(commands);
  // }
  Future<void> printEmptyLine({int count = 1, int feedLength = 5}) async {
    if (!isConnected) return;

    final commands = <int>[];
    for (int i = 0; i < count; i++) {
      commands.addAll([0x1B, 0x4A, feedLength]); // ESC J n (~0.625mm)
    }
    _socket!.add(commands);
    await _socket!.flush();
  }
  
  /// In dòng có 2 cột (trái - phải) với space between chính xác
  Future<void> printTwoColumns(
    String leftText,
    String rightText, {
    FontSettings? fontSettings,
  }) async {
    if (!isConnected) return;
    
    final settings = fontSettings ?? const FontSettings();
    final commands = <int>[]
    
    // ignore: prefer_spread_collections
    ..addAll(_applyFontSettings(settings))
    ..addAll([0x1B, 0x1D, 0x61, 0x00]); // Force left align
    
    // Sử dụng helper method để format
    final formattedLines = _formatItemWithPrice(leftText, rightText);
    
    for (final line in formattedLines) {
      commands.addAll('$line\n'.codeUnits);
    }
    
    _socket!.add(commands);
  }
  
  /// Reset formatting về mặc định
  Future<void> resetFormatting() async {
    if (!isConnected) return;
    
    _socket!.add(_getResetCommand());
  }
  
  /// Cắt giấy
  Future<void> cutPaper({bool fullCut = true}) async {
    if (!isConnected) return;
    
    final commands = <int>[];
    if (fullCut) {
      commands.addAll([0x1B, 0x64, 0x02]); // Full cut
    } else {
      commands.addAll([0x1B, 0x64, 0x01]); // Partial cut
    }
    
    _socket!.add(commands);
  }
  
  /// Flush data
  Future<void> flush() async {
    if (!isConnected) return;
    
    await _socket!.flush();
    await Future.delayed(const Duration(milliseconds: 500));
  }
  
  // ================== RECEIPT PRINTING METHODS ==================
  
  /// In header của receipt - FIX: wrap text và sử dụng FontSettings.title() với alignment chính xác
  Future<void> printReceiptHeader({
    required ReceiptTemplate template,
    required String restaurantName,
    String? address,
    String? phone,
    String? logoPath,
  }) async {
    // final isShowLogo = template.isShowBusinessLogo ?? false;
    final isShowBusinessName = template.isShowBusinessName ?? false;
    final isShowBusinessContact = template.isShowBusinessContact ?? false;
    if (!isConnected) return;
    
    // Logo (nếu có)
    // if (isShowLogo && logoPath != null) {
    //   await printText(
    //     '[ RESTAURANT LOGO ]',
    //     fontSettings: const FontSettings(),
    //     alignment: TextAlignment.center,
    //   );
    //   await printEmptyLine();
    // }
    
    if (isShowBusinessName) { // Restaurant name - sử dụng FontSettings.title() với text wrapping
      await printText(
        _sanitizeText(restaurantName),
        fontSettings: FontSettings.title(),
        alignment: TextAlignment.center,
        useEffectiveWidth: true, // Bật text wrapping dựa trên effective width
      ); 
    }
    
    if (isShowBusinessContact) {
      // Address
      if (address != null) {
        await printText(
          _sanitizeText(address),
          fontSettings: const FontSettings(),
          alignment: TextAlignment.center,
          useEffectiveWidth: true,
        );
      }

      // Phone
      if (phone != null) {
        await printText(
          phone,
          fontSettings: const FontSettings(style: TextPrintStyle.bold),
          alignment: TextAlignment.center,
          useEffectiveWidth: true,
        );
      }
    }

    // if (isShowLogo || isShowBusinessName || isShowBusinessContact) {
    if (isShowBusinessName || isShowBusinessContact) {
      await printSeparator();
    }
    
    await printEmptyLine();
  }
  
  /// In thông tin khách hàng
  Future<void> printCustomerInfo({
    required ReceiptTemplate template,
    required AppLocalizations l10n,
    required String customerName,
    required String orderTime,
    required String orderNumber,
    required String totalItems,
  }) async {
    final isShowCustomerInfo = template.isShowCustomerInfo ?? false;
    final isShowOrderInfo = template.isShowOrderInfo ?? false;
    
    if (!isConnected) return;
    
    if (isShowCustomerInfo) {
      await resetFormatting();
      await printText(
        l10n.receiptCustomerName,
        fontSettings: const FontSettings(),
      );
      await printText(
        _sanitizeText(customerName),
        fontSettings: FontSettings.subtitle(),
      );
      await resetFormatting();
      // await printEmptyLine();
    }
    
    // Order info
    if (isShowOrderInfo) {
      await printText(
        l10n.orderTime,
        fontSettings: const FontSettings(size: StarFontSize.normal),
        useEffectiveWidth: false,
      );
      await printText(
        _sanitizeText(orderTime),
        fontSettings: FontSettings.subtitle(),
      );
      await resetFormatting();
      await printText(
        l10n.receiptOrderNumber,
        fontSettings: const FontSettings(),
      );
      await printText(
        '#$orderNumber',
        fontSettings: FontSettings.subtitle(),
      );
      // await printEmptyLine();
      
      // Total items
      await resetFormatting();
      await printText(
        l10n.total,
        fontSettings: const FontSettings(),
      );
      await printText(
        _sanitizeText(totalItems),
        fontSettings: FontSettings.subtitle(),
      );
      await resetFormatting();
    }
    
    if (isShowCustomerInfo || isShowOrderInfo) {
      await printSeparator();
    }
    // await printEmptyLine();
  }
  
  /// In danh sách items với format chính xác
  Future<void> printItems({
    required ReceiptTemplate template,
    required Order orderPrint,
    required List<ItemOrder> items,
  }) async {
    final isShowOrderModifier = template.isShowOrderModifier ?? false;

    if (!isConnected) return;
    await resetFormatting(); // Đảm bảo font bình thường
    
    for (int i = 0; i < items.length; i++) {
      final item = items[i];
      final modifiers = item.modifiers ?? [];
      final quantity = item.quantity ?? 1;
      final totalPrice = item.subtotal ?? 0;
      final priceStr = CurrencyHelper.convertMoney(
        totalPrice.toString(),
        code: orderPrint.currencyCode ?? 'USD',
      );

      final itemText = _sanitizeText('$quantity  x  ${item.title}');
      
      // In dòng đầu với item name và price (luôn cùng dòng) - ITEM NAME ĐẬM HỚN
      final itemLines = _formatItemWithPrice(itemText, priceStr);
      final commands = <int>[]
      
      // Apply BOLD font settings cho item name
      // ignore: prefer_spread_collections
      ..addAll(_applyFontSettings(const FontSettings(style: TextPrintStyle.bold, emphasized: true)))
      ..addAll([0x1B, 0x1D, 0x61, 0x00]); // Left align
      
      // In tất cả các dòng của item
      for (final line in itemLines) {
        commands.addAll('$line\n'.codeUnits);
      }
      
      _socket?.add(commands);
      await printEmptyLine(count: 1, feedLength: 1);
      await resetFormatting();
      
      // In modifiers nếu có (KHÔNG thụt vào - canh lề trái với item name)
      if (isShowOrderModifier && modifiers.isNotEmpty) {
        for (final modifier in modifiers) {
          final String modifierText = _sanitizeText(modifier.option?.name);

          await printText(
            modifierText,
            fontSettings: const FontSettings(),
            useEffectiveWidth: false,
          );
          await printEmptyLine(
            count: 1,
            feedLength: 1,
          );
        }
      }
      
      // In dòng separator giữa các items
      await printSeparator(char: '-');
    }
  }
  
  /// In tổng tiền với format chính xác
  Future<void> printTotals({
    required AppLocalizations l10n,
    required String subtotal,
    required String tax,
    required String total,
  }) async {
    if (!isConnected) return;
    
    // await printSeparator();
    
    // Reset formatting trước khi in totals
    await resetFormatting();
    
    // Subtotal - normal font
    await printTwoColumns(l10n.subtotal, subtotal,
      fontSettings: const FontSettings());
    
    // Tax - normal font  
    await printTwoColumns(l10n.tax, tax,
      fontSettings: const FontSettings());
    
    // Total - bold font, có thể to hơn
    await printTwoColumns(l10n.total, total,
      fontSettings: const FontSettings(style: TextPrintStyle.bold, emphasized: true));
  }
  
  /// In footer - FIX: sử dụng font lớn với alignment chính xác và text wrapping
  Future<void> printReceiptFooter({
    required ReceiptTemplate template,
    String thankYouMessage = 'THANK YOU!',
  }) async {
    final isShowAdditionalText = template.footerText?.isNotEmpty ?? false;
    if (!isConnected) return;
    if (!isShowAdditionalText) return;
    await resetFormatting();
    await printSeparator();
    await printEmptyLine();
    await printText(
      removeDiacritics(thankYouMessage).toUpperCase(),
      fontSettings: const FontSettings(
        size: StarFontSize.normal,
        style: TextPrintStyle.bold,
        emphasized: true,
      ),
      alignment: TextAlignment.center,
      useEffectiveWidth: true,
    ); // Bật text wrapping cho footer
    await printEmptyLine(count: 4);
  }
  // Future<void> printReceiptFooter({String thankYouMessage = 'THANK YOU!'}) async {
  //   if (!isConnected) return;
    
  //   await printEmptyLine();
  //   await printText(thankYouMessage, 
  //     fontSettings: const FontSettings(size: StarFontSize.large, style: TextPrintStyle.bold, emphasized: true),
  //     alignment: TextAlignment.center,
  //     useEffectiveWidth: true); // Bật text wrapping cho footer
  //   await printEmptyLine(count: 4);
  // }




  // ================== IMAGE PROCESSING METHODS ==================
  
 /// In hình ảnh với STAR Line Mode commands
  Future<void> printImage(
    Uint8List imageBytes, {
    int? maxWidth,
    ImageAlignment alignment = ImageAlignment.center,
  }) async {
    if (!isConnected) return;
    
    try {
      // Convert image thành bitmap format cho STAR
      final bitmapData = await _convertImageToBitmap(imageBytes, maxWidth);
      if (bitmapData == null) return;
      
      // Tạo STAR commands cho in ảnh
      final commands = <int>[]
        // Set raster bit image mode
        ..addAll([0x1B, 0x30]) // ESC 0 - Initialize printer
        ..addAll(_getAlignmentCommandImage(alignment))
        ..addAll(_generateRasterCommand(bitmapData))
        ..addAll([0x0A]); // Line feed
      
      _socket!.add(commands);
    } catch (e) {
      print('Error printing image: $e');
    }
  }
  
  /// Convert image thành 1-bit bitmap cho STAR printer
  Future<Map<String, dynamic>?> _convertImageToBitmap(
    Uint8List imageBytes, 
    int? maxWidth
  ) async {
    try {
      final codec = await ui.instantiateImageCodec(imageBytes);
      final frame = await codec.getNextFrame();
      final image = frame.image;
      
      // Resize nếu cần
      int width = image.width;
      int height = image.height;
      
      if (maxWidth != null && width > maxWidth) {
        height = (height * maxWidth / width).round();
        width = maxWidth;
      }
      
      // Ensure width is multiple of 8 (byte boundary)
      final alignedWidth = (width + 7) ~/ 8 * 8;
      
      // Convert to grayscale and then to 1-bit
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);
      
      // Draw resized image
      canvas.drawImageRect(
        image,
        Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
        Rect.fromLTWH(0, 0, alignedWidth.toDouble(), height.toDouble()),
        Paint()..filterQuality = FilterQuality.high,
      );
      
      final picture = recorder.endRecording();
      final resizedImage = await picture.toImage(alignedWidth, height);
      
      // Convert to byte data
      final byteData = await resizedImage.toByteData(format: ui.ImageByteFormat.rawRgba);
      if (byteData == null) return null;
      
      // Convert RGBA to 1-bit bitmap
      final bitmap = _convertToMonochrome(byteData, alignedWidth, height);
      
      return {
        'width': alignedWidth,
        'height': height,
        'data': bitmap,
      };
    } catch (e) {
      print('Error converting image: $e');
      return null;
    }
  }
  
  /// Convert RGBA data thành monochrome bitmap
  Uint8List _convertToMonochrome(ByteData rgbaData, int width, int height) {
    final bytes = rgbaData.buffer.asUint8List();
    final bitmapBytes = <int>[];
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x += 8) {
        int byte = 0;
        for (int bit = 0; bit < 8 && (x + bit) < width; bit++) {
          final pixelIndex = (y * width + x + bit) * 4;
          if (pixelIndex + 2 < bytes.length) {
            // Calculate grayscale value
            final r = bytes[pixelIndex];
            final g = bytes[pixelIndex + 1];
            final b = bytes[pixelIndex + 2];
            final gray = (r * 0.299 + g * 0.587 + b * 0.114).round();
            
            // Threshold to black/white (adjust threshold as needed)
            if (gray < 128) {
              byte |= (0x80 >> bit);
            }
          }
        }
        bitmapBytes.add(byte);
      }
    }
    
    return Uint8List.fromList(bitmapBytes);
  }
  
  /// Generate STAR raster command
  List<int> _generateRasterCommand(Map<String, dynamic> bitmapData) {
    final width = bitmapData['width'] as int;
    final height = bitmapData['height'] as int;
    final data = bitmapData['data'] as Uint8List;
    
    final commands = <int>[];
    
    // STAR Line Mode raster bit image command
    // ESC * m n1 n2 [data...]
    commands.addAll([0x1B, 0x2A]); // ESC *
    commands.add(33); // m = 33 for 24-dot single-density
    commands.add((width ~/ 8) & 0xFF); // n1 (low byte of width in bytes)
    commands.add(((width ~/ 8) >> 8) & 0xFF); // n2 (high byte of width in bytes)
    
    // Add bitmap data
    commands.addAll(data);
    
    return commands;
  }
  
  /// Get alignment command
  List<int> _getAlignmentCommandImage(ImageAlignment alignment) {
    switch (alignment) {
      case ImageAlignment.left:
        return [0x1B, 0x1D, 0x61, 0x00]; // Left align
      case ImageAlignment.center:
        return [0x1B, 0x1D, 0x61, 0x01]; // Center align
      case ImageAlignment.right:
        return [0x1B, 0x1D, 0x61, 0x02]; // Right align
    }
  }

  Future<void> printReceiptWithImage() async {
    if (!await connect(
        ip: '*************',
        portNumber: 9100,
      )) {
        return ;
      }
  // Print header text
  await printText('RECEIPT HEADER', alignment: TextAlignment.center);
  
  // Load image from assets
  final ByteData imageData = await rootBundle.load('assets/icon/icon.png');
  final Uint8List imageBytes = imageData.buffer.asUint8List();
  
  // Print image
  await printImage(imageBytes, maxWidth: 200);
  
  // Print more text
  await printText('Thank you for your purchase!');
}


Future<void> printImageAlternative() async {
    if (!await connect(
        ip: '*************',
        portNumber: 9100,
      )) {
        return ;
      }
      final ByteData imageData = await rootBundle.load('assets/icon/icon.png');
  final Uint8List imageBytes = imageData.buffer.asUint8List();
    
    final bitmapData = await _convertImageToBitmap(imageBytes, null);
    if (bitmapData == null) return;
    
    final width = bitmapData['width'] as int;
    final height = bitmapData['height'] as int;
    final data = bitmapData['data'] as Uint8List;
    
    final commands = <int>[]
      // ESC GS v 0 command for raster graphics
      ..addAll([0x1B, 0x1D, 0x76, 0x30, 0x00]) // ESC GS v 0
      ..addAll([
        (width ~/ 8) & 0xFF,
        ((width ~/ 8) >> 8) & 0xFF,
        height & 0xFF,
        (height >> 8) & 0xFF,
      ])
      ..addAll(data)
      ..addAll([0x0A]); // Line feed
    
    _socket!.add(commands);
  }


Future<StarXpandPrinter?> connectToIPAddress(String ipAddress) async {
    try {
      // Tạo printer object với IP address
      var printer = StarXpandPrinter(
        identifier: 'TCP:$ipAddress', // Format TCP:IP_ADDRESS
        model: 'TSP800II',
        emulation: 'StarLine',
      );
      
      _connectedPrinter = printer;
      return printer;
    } catch (e) {
      print('Error connecting to $ipAddress: $e');
      return null;
    }
  }

  /////////////// ==================== TEST IMAGE


  
  /// In hoá đơn hoàn chỉnh
  Future<bool> printReceipt({
    required BuildContext context,
    required AppLocalizations l10n,
    required String ipAddress,
    required Order order,
    String? logoPath,
    String? printerType,
    bool isPrintTest = false,  
    int? portNumber,

  }) async {
    // final l10n = context.l10n;
    final Order orderPrint = isPrintTest ? orderTest : order;
    final List<ItemOrder> receiptItems = orderPrint.items ?? [];
    final business = context.read<AuthBloc>().state.business;
    final String timeZone = context.read<AuthBloc>().state.businessTimeZone.toString();

    
    try {
      final ReceiptTemplate? template = await AppLocalStorage.getCustomerReceipt();
      AppLog.d('template == ${jsonEncode(template)}');
      
      // final isShowLogo = template?.isShowBusinessLogo ?? false;
      // final businessLogo = logoPath ?? business?.logo ?? '';
      final businessName = business?.name ?? '';
      final businessStreet = business?.address?.street ?? '';
      final businessCity = business?.address?.city ?? '';
      final businessState = business?.address?.state ?? '';
      final businessCountry = business?.address?.country ?? '';
      final businessAddress = combineAddress(
        street: businessStreet,
        city: businessCity,
        country: businessCountry,
        state: businessState,
      );
      final businessPhone = business?.phone ?? '';
      final phone = AppValidations.formatPhoneNumber(businessPhone);
      final customerName = orderPrint.customerName ?? orderPrint.customer?.displayName ?? '';
      final orderNumber = orderPrint.orderNumber ?? '';
      // final totalItems = receiptItems.length;
      final totalItems = getTotalQuantity(order, isPrintTest: isPrintTest);
      final totalItemsText = totalItems > 1 ? l10n.items.toCapitalizedWords() : l10n.item.toCapitalizedWords();
      final subTotal = orderPrint.subtotal ?? 0;
      final subTotalText = CurrencyHelper.convertMoney(subTotal.toString(), code: orderPrint.currencyCode ?? 'USD');
      final tax = orderPrint.taxTotal ?? 0;
      final taxText = CurrencyHelper.convertMoney(tax.toString(), code: orderPrint.currencyCode ?? 'USD');
      final total = orderPrint.total ?? 0;
      final totalText = CurrencyHelper.convertMoney(total.toString(), code: orderPrint.currencyCode ?? 'USD');
      final orderTime = orderPrint.createdAt ?? orderPrint.updatedAt ?? DateTime.now();
      final orderTimeFormat = DateTimeHelper.dateTimeLongFormat(
        orderTime,
        timeZone: timeZone,
        isWeekDay: false,
        langLocale: Localizations.localeOf(context).languageCode,
        format: 'd MMM, y - hh:mm a',
      );
      final additionalText = template?.footerText ?? l10n.thanksYou;

      print('totalItems == $totalItems // $totalItemsText');

      
      // Kết nối
      if (!await connect(
        ip: ipAddress,
        portNumber: portNumber,
      )) {
        return false;
      }
      
      // Initialize
      await initialize();
      
      // Header
      await printReceiptHeader(
        template: template ?? ReceiptTemplate(),
        restaurantName: businessName,
        address: businessAddress,
        phone: phone,
        logoPath: logoPath,
      );
      
      // Customer info
      await printCustomerInfo(
        template: template ?? ReceiptTemplate(),
        l10n: l10n,
        customerName: customerName,
        orderTime: orderTimeFormat,
        orderNumber: orderNumber,
        totalItems: _sanitizeText('$totalItems $totalItemsText'),
      );
      
      // // Items
      await printItems(
        template: template ?? ReceiptTemplate(),
        orderPrint: orderPrint,
        items: receiptItems,
      );
      
      await printTotals(
        l10n: l10n,
        subtotal: subTotalText,
        tax: taxText,
        total: totalText,
      );
      
      // Footer
      await printReceiptFooter(
        template: template ?? ReceiptTemplate(),
        thankYouMessage: additionalText,
      );
      
      // Cut paper
      await cutPaper();
      
      // Flush and disconnect
      await flush();
      await disconnect();
      
      print('In hoa don STAR thanh cong');
      return true;
    } catch (e) {
      print('Loi in hoa don STAR: $e');
      await disconnect();
      return false;
    }
  }

  String combineAddress({
    String? street,
    String? city,
    String? country,
    String? state,
  }) {
    // Tạo danh sách các field không rỗng
    final List<String> addressParts = [
      if (street != null && street.isNotEmpty) street,
      if (city != null && city.isNotEmpty) city,
      if (state != null && state.isNotEmpty) state,
      if (country != null && country.isNotEmpty) country,
    ];

    // Ghép các phần tử với dấu phẩy
    return addressParts.join(', ');
  }

  int getTotalQuantity(
    Order order, {
    bool isPrintTest = false,
  }) {
    if (isPrintTest) return 2;
    final items = order.items ?? [];
    return items.fold(0, (sum, item) => sum + (item.quantity ?? 0));
  }

  // Hàm helper để sanitize text
  String _sanitizeText(String? text) {
    if (text == null || text.isEmpty) return '';
    return removeDiacritics(text);
  }
  
}

// ================== USAGE EXAMPLES ==================

// // Ví dụ sử dụng đơn giản
// Future<void> exampleUsage() async {
//   // Tạo instance
//   final printer = StarPrinterReceipt(ipAddress: '*************');
  
//   // Tạo danh sách items với modifiers
//   final items = [
//     ReceiptItem(
//       name: 'Charbroiled Pork, Beef or Chicken & Charbroiled Shrimp over Vermicelli', 
//       price: 160.75, 
//       quantity: 1,
//       modifiers: ['Chow Mein'],
//     ),
//     ReceiptItem(
//       name: 'Charbroiled Pork, Beef or Chicken & Charbroiled Shrimp over Vermicelli', 
//       price: 160.75, 
//       quantity: 1,
//       modifiers: ['Chow Mein', 'Pork'],
//     ),
//   ];
  
//   // In hóa đơn
//   final success = await printer.printReceipt(
//     receiptItems: items,
//     cash: 50.0,
//     cashierName: 'John Doe',
//     customerName: 'Julie Pham',
//     orderNumber: '#4322',
//   );
  
//   if (success) {
//     print('In hóa đơn thành công!');
//   } else {
//     print('Lỗi in hóa đơn!');
//   }
// }

// // Ví dụ sử dụng nâng cao với custom formatting
// Future<void> advancedUsage() async {
//   final printer = StarPrinterReceipt(ipAddress: '*************');
  
//   if (await printer.connect()) {
//     await printer.initialize();
    
//     // Custom header
//     await printer.printText('MY RESTAURANT', 
//       fontSettings: FontSettings.title(),
//       alignment: TextAlignment.center);
    
//     await printer.printText('Special Promotion!', 
//       fontSettings: const FontSettings(
//         size: StarFontSize.wide,
//         style: TextPrintStyle.inverse,
//       ),
//       alignment: TextAlignment.center);
    
//     await printer.printEmptyLine();
    
//     // Custom content
//     await printer.printTwoColumns('Item:', 'Price:',
//       fontSettings: const FontSettings(style: TextPrintStyle.bold));
    
//     await printer.printSeparator();
    
//     await printer.printTwoColumns('Special Pho', r'$15.99');
//     await printer.printText('  With extra beef and noodles', 
//       fontSettings: FontSettings.description());
    
//     await printer.printEmptyLine();
//     await printer.printReceiptFooter(thankYouMessage: 'Come Back Soon!');
    
//     await printer.cutPaper();
//     await printer.flush();
//     await printer.disconnect();
//   }
// }

// ALTERNATIVE: WebPRNT HTTP approach
// BƯỚC 1: Kiểm tra máy in đang ở mode nào
class StarPrinterDebug {
  final String ipAddress;
  final int port;
  Socket? _socket;
  
  StarPrinterDebug(this.ipAddress, {this.port = 9100});
  
  Future<bool> connect() async {
    try {
      _socket = await Socket.connect(ipAddress, port);
      return true;
    } catch (e) {
      print('Connection error: $e');
      return false;
    }
  }
  
  Future<void> disconnect() async {
    await _socket?.close();
    _socket = null;
  }
  
  Future<void> flush() async {
    await _socket?.flush();
    await Future.delayed(Duration(milliseconds: 500));
  }
  
  // TEST 1: Kiểm tra máy in có nhận command không
  Future<void> testBasicConnection() async {
    print("=== TEST 1: Basic Connection ===");
    
    final commands = <int>[
      0x1B, 0x40, // Initialize
      ...('TEST BASIC CONNECTION\n').codeUnits,
      0x0A, 0x0A, // Line feeds
    ];
    
    _socket!.add(commands);
    await flush();
    print("Sent basic test - check if printer prints this");
  }

  // TEST 2: Kiểm tra máy in mode (ESC/POS hay Star Line Mode)
  Future<void> testPrinterMode() async {
    print("=== TEST 2: Printer Mode Detection ===");
    
    // Test ESC/POS mode
    final escPosTest = <int>[
      0x1B, 0x40, // ESC @ - Initialize
      ...('ESC/POS MODE TEST\n').codeUnits,
      0x1B, 0x45, // Bold ON
      ...('BOLD TEXT\n').codeUnits,
      0x1B, 0x46, // Bold OFF
      ...('NORMAL TEXT\n').codeUnits,
      0x0A, 0x0A,
    ];
    
    _socket!.add(escPosTest);
    await flush();
    await Future.delayed(Duration(seconds: 2));
    
    // Test Star Line Mode
    final starLineTest = <int>[
      0x1B, 0x1D, 0x49, 0x42, 0x00, // Star Line Mode Initialize
      ...('STAR LINE MODE TEST\n').codeUnits,
      0x1B, 0x45, // Emphasized
      ...('EMPHASIZED TEXT\n').codeUnits,
      0x1B, 0x46, // Normal
      ...('NORMAL TEXT\n').codeUnits,
      0x0A, 0x0A,
    ];
    
    _socket!.add(starLineTest);
    await flush();
    
    print("Check which mode works better");
  }

  // TEST 3: Test đơn giản nhất cho image - chỉ 1 dot
  Future<void> testSingleDot() async {
    print("=== TEST 3: Single Dot Test ===");
    
    // ESC/POS single dot bitmap
    final singleDotESC = <int>[
      0x1B, 0x40, // Initialize
      ...('SINGLE DOT ESC/POS:\n').codeUnits,
      0x1B, 0x2A, 0x00, 0x01, 0x00, // ESC * 0 1 0 (single column bitmap)
      0x80, // Single dot (bit 7 = top dot)
      0x0A, 0x0A,
    ];
    
    _socket!.add(singleDotESC);
    await flush();
    await Future.delayed(Duration(seconds: 2));
    
    // Star Line Mode single dot
    final singleDotStar = <int>[
      ...('SINGLE DOT STAR:\n').codeUnits,
      0x1B, 0x4B, 0x01, 0x00, // Star bitmap command
      0x80, // Single dot
      0x0A, 0x0A,
    ];
    
    _socket!.add(singleDotStar);
    await flush();
    await Future.delayed(Duration(seconds: 2));
    
    // GS v 0 command (modern ESC/POS)
    final gsV0Test = <int>[
      ...('GS v 0 TEST:\n').codeUnits,
      0x1D, 0x76, 0x30, 0x00, // GS v 0 0
      0x01, 0x00, // width = 1
      0x01, 0x00, // height = 1
      0x80, // Single dot
      0x0A, 0x0A,
    ];
    
    _socket!.add(gsV0Test);
    await flush();
    
    print("Check which command produces a single black dot");
  }

  // TEST 4: Test với pattern đơn giản
  Future<void> testSimplePattern() async {
    print("=== TEST 4: Simple Pattern Test ===");
    
    // 8x8 square pattern
    final pattern8x8 = <int>[
      ...('8x8 SQUARE PATTERN:\n').codeUnits,
      0x1D, 0x76, 0x30, 0x00, // GS v 0 0 (modern bitmap)
      0x01, 0x00, // width = 1 byte
      0x08, 0x00, // height = 8 dots
      0xFF, // 11111111
      0x81, // 10000001
      0x81, // 10000001
      0x81, // 10000001
      0x81, // 10000001
      0x81, // 10000001
      0x81, // 10000001
      0xFF, // 11111111
      0x0A, 0x0A,
    ];
    
    _socket!.add(pattern8x8);
    await flush();
    await Future.delayed(Duration(seconds: 2));
    
    // Try với ESC * command
    final patternESC = <int>[
      ...('ESC * PATTERN:\n').codeUnits,
      0x1B, 0x2A, 0x21, 0x01, 0x00, // ESC * ! 1 0 (double density)
      0xFF, // Vertical line
      0x0A, 0x0A,
    ];
    
    _socket!.add(patternESC);
    await flush();
    
    print("Check if you see a square or vertical line");
  }

  // TEST 5: Kiểm tra specific Star TSP800II commands
  Future<void> testTSP800IICommands() async {
    print("=== TEST 5: TSP800II Specific Commands ===");
    
    // TSP800II có thể cần initialize khác
    final tsp800Init = <int>[
      0x1B, 0x40, // ESC @ - Initialize
      0x1B, 0x52, 0x00, // ESC R 0 - International character set
      0x1B, 0x74, 0x00, // ESC t 0 - Character code table
      ...('TSP800II INIT TEST\n').codeUnits,
    ];
    
    _socket!.add(tsp800Init);
    await flush();
    await Future.delayed(Duration(seconds: 1));
    
    // Test wide format printing (112mm width)
    final wideTest = <int>[
      ...('WIDE FORMAT TEST: ').codeUnits,
      ...('X' * 60).codeUnits, // 60 characters should fit on 112mm
      ...('\n\n').codeUnits,
    ];
    
    _socket!.add(wideTest);
    await flush();
    
    print("Check if wide format printing works correctly");
  }

  // TEST 6: WebPRNT alternative test
  Future<void> testWebPRNTAlternative() async {
    print("=== TEST 6: WebPRNT Alternative ===");
    
    // TSP800II hỗ trợ WebPRNT, có thể thử HTTP instead of socket
    print("Consider using HTTP WebPRNT instead of raw socket");
    print("WebPRNT endpoint: http://${ipAddress}/StarWebPRNT/SendMessage");
    
    // Basic WebPRNT XML for image
    final webPRNTXML = '''
      <StarWebPRNT>
        <Text>WebPRNT Image Test</Text>
        <Bitmap Source="assets/logo.png" Width="200"/>
        <Cut Type="Full"/>
      </StarWebPRNT>
    ''';
    
    print("WebPRNT XML example: $webPRNTXML");
    print("Try using HTTP POST with this XML to WebPRNT endpoint");
  }

  // MAIN DEBUG FUNCTION
  Future<void> runAllTests() async {
    if (!await connect()) {
      print("Cannot connect to printer!");
      return;
    }
    
    print("Starting STAR TSP800II Debug Tests...");
    print("Watch the printer output carefully!");
    print("=" * 50);
    
    await testBasicConnection();
    await Future.delayed(Duration(seconds: 3));
    
    await testPrinterMode();
    await Future.delayed(Duration(seconds: 3));
    
    await testSingleDot();
    await Future.delayed(Duration(seconds: 3));
    
    await testSimplePattern();
    await Future.delayed(Duration(seconds: 3));
    
    await testTSP800IICommands();
    await Future.delayed(Duration(seconds: 3));
    
    await testWebPRNTAlternative();
    
    await disconnect();
    
    print("=" * 50);
    print("Debug tests completed!");
    print("Report back which tests worked and which didn't.");
  }
}

// ALTERNATIVE: WebPRNT HTTP approach
class StarWebPRNT {
  final String ipAddress;
  
  StarWebPRNT(this.ipAddress);
  
  Future<bool> printImageViaWebPRNT(String imagePath) async {
    try {
      final url = 'http://$ipAddress/StarWebPRNT/SendMessage';
      
      final xml = '''<?xml version="1.0" encoding="UTF-8"?>
<StarWebPRNT>
  <Text Alignment="Center">LOGO TEST</Text>
  <Bitmap Source="$imagePath" Width="300"/>
  <LineFeed/>
  <Text>Image printed via WebPRNT</Text>
  <Cut Type="Full"/>
</StarWebPRNT>''';

      // Use http package để POST XML
      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'text/xml'},
        body: xml,
      );
      
      return response.statusCode == 200;
    } catch (e) {
      print('WebPRNT error: $e');
      return false;
    }
  }
}
